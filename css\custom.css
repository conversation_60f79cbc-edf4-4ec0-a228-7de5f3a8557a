/* ----------------------------------------------------------------------------------------
* Author        : Awaiken
* Template Name : Nextmind - AI Agency & Technology HTML Template
* File          : CSS File
* Version       : 1.0
* ---------------------------------------------------------------------------------------- */
/* INDEX
----------------------------------------------------------------------------------------
01. Global Variables
02. General css
03. Header css
04. Hero css
05. Scrolling Ticker css
06. About Us css
07. Our Services css
08. Why Choose Us css
09. How It Work css
10. Our Projects css
11. Our Testimonials css
12. Our Faqs css
13. CTA Box css
14. Our Blog css
15. Footer css
16. About Us Page css
17. Services Page css
18. Service Single css
19. Blog Archive css
20. Blog Single css
21. Projects Page css
22. Project Single css
23. Team Page css
24. Team Single css
25. Testimonials Page css
26. Image Gallery css
27. Video Gallery css
28. FAQs Page css
29. Contact Us Page css
30. 404 Error Page css
31. Responsive css
-------------------------------------------------------------------------------------- */

/************************************/
/*** 	 01. Global Variables	  ***/
/************************************/

:root{
	--primary-color				: #FFFFFF;
	--secondary-color			: #FFFFFF0A;
	--text-color				: #A7AABB;
	--bg-color					: #000000;
	--accent-color				: #E24C4A;
	--accent-secondary-color	: #386BB7;
	--divider-color				: #FFFFFF0F;
	--dark-divider-color		: #FFFFFF66;
	--error-color				: rgb(230, 87, 87);
	--default-font				: "Manrope", sans-serif;
}

/************************************/
/*** 	   02. General css		  ***/
/************************************/

body{
	position: relative;
	font-family: var(--default-font);
	font-size: 16px;
	font-weight: 400;
	line-height: 1.1em;
	color: var(--text-color);
    background: var(--bg-color);
}

::-webkit-scrollbar-track{
	background-color: var(--primary-color);
	border-left: 1px solid var(--primary-color);
}
::-webkit-scrollbar{
	width: 7px;
	background-color: var(--primary-color);
}
::-webkit-scrollbar-thumb{
	background: linear-gradient(180deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
}

::selection{
	color: var(--primary-color);
	background-color: var(--accent-color);
	filter: invert(1);
}

p{
	line-height: 1.6em;
	margin-bottom: 1.6em;
}

h1,
h2,
h3,
h4,
h5,
h6{
	margin :0;
	font-weight: 700;
	line-height: 1.2em;
	color: var(--primary-color);
}

figure{
	margin: 0;
}

img{
	max-width: 100%;
}

a{
	text-decoration: none;
}

a:hover{
	text-decoration: none;
	outline: 0;
}

a:focus{
	text-decoration: none;
	outline: 0;
}

html,
body{
	width: 100%;
	overflow-x: clip;
}

.container{
	max-width: 1300px;
}

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl{
	position: relative;
    padding-right: 15px;
    padding-left: 15px;
	z-index: 1;
}

.image-anime{
	position: relative;
	overflow: hidden;
}

.image-anime:after{
	content: "";
	position: absolute;
    width: 200%;
    height: 0%;
    left: 50%;
    top: 50%;
    background-color: rgba(255,255,255,.3);
    transform: translate(-50%,-50%) rotate(-45deg);
    z-index: 1;
}

.image-anime:hover:after{
    height: 250%;
    transition: all 600ms linear;
    background-color: transparent;
}

.reveal{
	position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    visibility: hidden;
    overflow: hidden;
}

.reveal img{
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -webkit-transform-origin: left;
    transform-origin: left;
}

.row{
    margin-right: -15px;
    margin-left: -15px;
}

.row > *{
	padding-right: 15px;
	padding-left: 15px;
}

.row.no-gutters{
    margin-right: 0px;
    margin-left: 0px;
}

.row.no-gutters > *{
    padding-right: 0px;
    padding-left: 0px;
}

.btn-default{
	position: relative;
	display: inline-block;
	background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
	background-size: 200% auto;
    border-radius: 100px;
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 700;
	line-height: 1em;
    text-transform: capitalize;
    border: none;
    padding: 17px 40px 17px 20px;
    transition: all 0.4s ease-in-out;
	overflow: hidden;
	z-index: 0;
}

.btn-default:hover{
	background-position: right center;
}

.btn-default::before{
	content: '';
	position: absolute;
	top: 50%;
    right: 20px;
	width: 10px;
	height: 10px;
	background: url('../images/arrow-white.svg') no-repeat;
    background-position: center center;
    background-size: cover;
	transform: translateY(-50%);
	transition: all 0.3s ease-in-out;
	z-index: 1;
}

.btn-default:hover::before{
	transform: translateY(-50%) rotate(45deg);
}

.readmore-btn{
	position: relative;
	font-size: 16px;
	font-weight: 700;
	text-transform: capitalize;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
    background-size: 200% auto;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.4s ease-in-out;
	padding-right: 20px;
	transition: all 0.3s ease-in-out;
}

.readmore-btn:hover{
	background-position: right center;
}

.readmore-btn:after{
	content: '';
    position: absolute;
	right: 0;
	top: 50%;
    transform: translateY(-50%);
    background: url('../images/arrow-gradiant.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
	width: 12px;
	height: 12px;
	transition: all 0.3s ease-in-out;
}

.readmore-btn:hover:after{
	transform: translateY(-50%) rotate(45deg);
}

.cb-cursor:before{
	background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
}

.preloader{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	background: var(--bg-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-container,
.loading{
	height: 100px;
	position: relative;
	width: 100px;
	border-radius: 100%;
}

.loading-container{
	margin: 40px auto;
}

.loading{
	border: 1px solid transparent;
	border-color: transparent var(--accent-secondary-color) transparent var(--accent-color);
	animation: rotate-loading 1.5s linear 0s infinite normal;
	transform-origin: 50% 50%;
}

.loading-container:hover .loading,
.loading-container .loading{
	transition: all 0.5s ease-in-out;
}

#loading-icon{
	position: absolute;
	top: 50%;
	left: 50%;
	max-width: 66px;
	transform: translate(-50%, -50%);
}

@keyframes rotate-loading{
	0%{
		transform: rotate(0deg);
	}

	100%{
		transform: rotate(360deg);
	}
}

.bg-section{
	width: 100%;
	max-width: 1600px;
    background-color: var(--secondary-color);
	background-image: url('../images/section-bg-shape.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
    border: 1px solid var(--divider-color);
	border-radius: 30px;
	margin: 0 auto;
}

.section-row{
	margin-bottom: 80px;
}

.section-row .section-title{
	margin-bottom: 0;
}

.section-title.section-title-center{
	width: 100%;
	max-width: 710px;
	text-align: center;
	margin: 0 auto;
}

.section-title{
	margin-bottom: 40px;
}

.section-title h3{
	display: inline-block;
	font-size: 14px;
    font-weight: 500;
	line-height: 1.357em;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--primary-color);
	background: var(--divider-color);
	border: 1px solid var(--divider-color);
	border-radius: 100px;
	padding: 9px 24px;
    margin-bottom: 20px;
}

.section-title h1{
	font-size: 70px;
	font-weight: 300;
    letter-spacing: -0.03em;
	margin-bottom: 0;
	cursor: none;
}

.section-title h2{
	font-size: 48px;
	font-weight: 600;
	margin-bottom: 0;
	cursor: none;
}

.section-title p{
	margin-top: 20px;
	margin-bottom: 0;
}

.section-title-content p{
	margin: 0;
}

.section-btn{
	text-align: right;
}

.section-content-btn .section-btn{
	text-align: left;
	margin-top: 30px;
}

.help-block.with-errors ul{
	margin: 0;
	text-align: left;
}

.help-block.with-errors ul li{
	color: var(--error-color);
	font-weight: 500;
	font-size: 14px;
}

/************************************/
/**** 	   03. Header css		 ****/
/************************************/

header.main-header{
	position: absolute;
	top: 0;
	width: 100%;
    border-bottom: 1px solid var(--divider-color);
	z-index: 100;
}

header.main-header .header-sticky{
	position: relative;
	top: 0;
	z-index: 100;
}

header.main-header .header-sticky.hide{
	transform: translateY(-100%);
	transition: transform 0.3s ease-in-out;
	border-radius: 0;
}

header.main-header .header-sticky.active{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	border-radius: 0;
    transform: translateY(0);
	background: var(--bg-color);
	border-bottom: 1px solid var(--divider-color);
}

.navbar{
	padding: 30px 0;
	align-items: center;
}

.navbar-brand{
    padding: 0;
	margin: 0;
}

.main-menu .nav-menu-wrapper{
	flex: 1;
	text-align: center;
	margin: 0 2.083vw;
}

.main-menu .nav-menu-wrapper > ul{
	align-items: center;
	display: inline-flex;
}

.main-menu ul li{
	position: relative;
	margin: 0;
}

.main-menu ul li a{
	font-size: 16px;
	font-weight: 500;
	line-height: normal;
	padding: 13px 15px !important;
	color: var(--primary-color);
	text-transform: capitalize;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li.submenu > a:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 14px;
	margin-left: 8px;
    margin-top: 4px;
}

.main-menu ul li a:hover,
.main-menu ul li a:focus{
	color: var(--accent-color);
}

.main-menu ul li a:focus-visible{
    box-shadow: none;
}

.main-menu ul ul{
	visibility: hidden;
	opacity: 0;
	transform: scale(1,0.8);
	transform-origin: top;
	padding: 0;
	margin: 0;
	list-style: none;
	width: 235px;
	border-radius: 20px;
	position: absolute;
	left: 0;
	top: 100%;
    background: linear-gradient(110deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
	transition: all 0.3s ease-in-out;
	text-align: left;
}

.main-menu ul li.submenu:first-child ul{
    width: 235px;
}

.main-menu ul ul ul{
	left: 100%;
	top: 0;
	text-align: left;
}

.main-menu ul li:hover > ul{
	visibility: visible;
	opacity: 1;
	transform: scale(1,1);
    padding: 5px 0;
}

.main-menu ul li.submenu ul li.submenu > a:after{
    content: '\f105';
    float: right;
}

.main-menu ul ul li{
	margin: 0;
	padding: 0;
}

.main-menu ul ul li a{
	color: var(--primary-color);
	padding: 6px 20px !important;
	margin: 0;
	transition: all 0.3s ease-in-out;
}

.main-menu ul ul li a:hover,
.main-menu ul ul li a:focus{
	color: var(--bg-color);
	background-color: transparent;
	padding: 6px 20px 6px 23px !important;
}

.main-menu ul li.highlighted-menu{
    display: none;
}

.responsive-menu,
.navbar-toggle{
	display: none;
}

.responsive-menu{
	top: 0;
	position: relative;
}

.slicknav_btn{
	background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
	background-size: 200% auto;
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 38px;
	height: 38px;
	margin: 0;
	border-radius: 8px;
	transition: all 0.4s ease-in-out;
}

.navbar-toggle a.slicknav_btn.slicknav_open{
	background-position: right center;
}

.slicknav_icon .slicknav_icon-bar{
	display: block;
	width: 100%;
	height: 3px;
	width: 22px;
	background-color: var(--primary-color);
	border-radius: 6px;
	margin: 4px auto !important;
	transition: all 0.1s ease-in-out;
}

.slicknav_icon .slicknav_icon-bar:first-child{
	margin-top: 0 !important;
}

.slicknav_icon .slicknav_icon-bar:last-child{
	margin-bottom: 0 !important;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(1){
    transform: rotate(-45deg) translate(-5px, 5px);
	background-color: var(--primary-color);
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(2){
    opacity: 0;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(3){
    transform: rotate(45deg) translate(-5px, -5px);
	background-color: var(--primary-color);
}

.slicknav_menu{
	position: absolute;
    width: 100%;
	padding: 0;
	background: linear-gradient(110deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
}

.slicknav_menu ul{
	margin: 5px 0;
}

.slicknav_menu ul ul{
	margin: 0;
}

.slicknav_nav .slicknav_row,
.slicknav_nav li a{
	position: relative;
	font-size: 16px;
	font-weight: 500;
	text-transform: capitalize;
	padding: 7px 20px;
	color: var(--primary-color);
	line-height: normal;
	margin: 0;
	border-radius: 0 !important;
	transition: all 0.3s ease-in-out;
}

.slicknav_nav a:hover,
.slicknav_nav a:focus,	
.slicknav_nav .slicknav_row:hover{
	background-color: transparent;
	color: var(--bg-color);
}

.slicknav_menu ul ul li a{
    padding: 7px 20px 7px 30px;
}

.slicknav_arrow{
	font-size: 0 !important;
}

.slicknav_arrow:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 12px;
	margin-left: 8px;
	color: var(--primary-color);
	position: absolute;
	right: 15px;
	top: 50%;
	transform: translateY(-50%);
	transition: all 0.3s ease-out;
}

.slicknav_open > a .slicknav_arrow:after{
    transform: translateY(-50%) rotate(-180deg);
	color: var(--bg-color);
}

/************************************/
/***        04. Hero css	      ***/
/************************************/

.hero{
    background: url('../images/hero-bg-shape.png') no-repeat;
    background-position: top center;
    background-size: auto;
	padding: 235px 0 0;
    overflow: hidden;
}

.hero.hero-bg-image{
	position: relative;
	background: url('../images/hero-bg.jpg') no-repeat;
	background-position: center center;
	background-size: cover;
    padding: 240px 0 125px;
}

.hero.hero-bg-image::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-color);
    opacity: 75%;
    width: 100%;
    height: 100%;
    animation: none;
    z-index: 1;
}

.hero.hero-bg-image.hero-video .hero-bg-video{
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
}

.hero.hero-bg-image.hero-video .hero-bg-video video{
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.hero-content{
    position: relative;
    text-align: center;
    margin-bottom: 120px;
}

.hero.hero-bg-image .hero-content{
    margin: 0;
}

.hero.hero-bg-image .hero-icon-boxes{
    display: none;
}

.hero-content .section-title.section-title-center{
    position: relative;
    margin-bottom: 60px;
    z-index: 1;
}

.hero-content .section-title.section-title-center p{
    width: 100%;
    max-width: 600px;
    margin: 20px auto 0;
}

.hero-btn{
    position: relative;
    z-index: 1;
}

.hero-btn .btn-default{
    height: 100px;
    width: 100px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0;
}

.hero-btn .btn-default::before{
    display: none;
}

.hero-btn .btn-default img{
    width: 100%;
    max-width: 54px;
    transform: rotate(-45deg);
    transition: all 0.3s ease-in-out;
}

.hero-btn .btn-default:hover img{
    transform: rotate(0);
}

.hero-icon-box-1,
.hero-icon-box-2{
    position: absolute;
    left: 50%;
    width: 100%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
}

.hero-icon-box-1{
    top: 0;
    max-width: 82%;
}

.hero-icon-box-2{
    bottom: 20%;
}

.hero-icon{
    height: 60px;
    width: 60px;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
    background-size: 200% auto;
    border-radius: 100px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition: all 0.4s ease-in-out;
}

.hero-icon:hover{
    background-position: right center;
}

.hero-icon img{
    width: 100%;
    max-width: 30px;
}

.hero-icon-1,
.hero-icon-2{
    animation: iconheroimgdown 6s infinite linear alternate;
}

@keyframes iconheroimgdown{
    50%{
        transform: translateY(50px)
    }
}

.hero-icon-3,
.hero-icon-4{
    animation: iconheroimgup 6s infinite linear alternate;
}

@keyframes iconheroimgup{
    50%{
        transform: translateY(-50px)
    }
}

.hero-icon-2,
.hero-icon-4{
    animation-delay: 1s;
}

.hero-images{
    position: relative;
    width: 100%;
    max-width: 1220px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    margin-bottom: -118px;
}

.hero-image-2{
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 475px;
    box-shadow: 0px 40px 30px 0px #00000080;
    z-index: 1;
}

.hero-image-1,
.hero-image-3{
    width: 100%;
    max-width: 38%;
    margin-top: 80px;
}

.hero-image-1{
    transform: rotate(-3deg);
}

.hero-image-3{
    transform: rotate(3deg);
}

.hero-image-1 figure,
.hero-image-2 figure,
.hero-image-3 figure{
    display: block;
    border: 1px solid var(--divider-color);
    border-radius: 12px;
}

.hero-image-1 img,
.hero-image-2 img,
.hero-image-3 img{
    width: 100%;
    aspect-ratio: 1 / 1.182;
    object-fit: cover;
    border-radius: 12px;
}

/************************************/
/***   05. Scrolling Ticker css   ***/
/************************************/

.our-scrolling-ticker{
	background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
	padding: 30px 0;
}

.scrolling-ticker-box{
	--gap: 60px;
	position: relative;
	display: flex;
	overflow: hidden;
	user-select: none;
	gap: var(--gap);
	align-items: center;
}

.scrolling-content{
	flex-shrink: 0;
	display: flex;
	gap: var(--gap);
	min-width: 100%;
	animation: scroll 30s linear infinite;
}

@keyframes scroll{
	from{
		transform: translateX(0);
	}

	to{
		transform: translateX(calc(-100% - var(--gap)));
	}
}

.scrolling-content img{
	width: 100%;
	max-width: 176px;
	height: 40px;
}

/************************************/
/*** 	   06. About Us css		  ***/
/************************************/

.about-us{
    padding: 160px 0;
}

.about-image-box{
    margin-right: 30px;
}

.about-us-counters{
    display: flex;
    flex-wrap: wrap;
    gap: 30px 60px;
    margin-bottom: 40px;
}

.about-counter-item h2{
    font-size: 48px;
    display: inline-block;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
    background-size: 200% auto;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.4s ease-in-out;
    margin-bottom: 10px;
}

.about-counter-item:hover h2{
    background-position: right center;
}

.about-counter-item p{
    margin-bottom: 0;
}

.about-us-image{
    position: relative;
}

.about-us-image figure{
    display: block;
    border-radius: 12px;
}

.about-us-image figure img{
    width: 100%;
    aspect-ratio: 1 / 1.045;
    object-fit: cover;
    border-radius: 12px;
}

.about-info-box{
    position: absolute;
    bottom: 20px;
    left: 20px;
    width: 100%;
    max-width: 250px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background-color: var(--divider-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 20px;
    z-index: 1;
}

.about-info-box .icon-box{
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
    background-size: 200% auto;
    border-radius: 50%;
    margin-right: 15px;
    transition: all 0.3s ease-in-out;
}

.about-info-box:hover .icon-box{
	background-position: right center;
}

.about-info-box .icon-box img{
    width: 100%;
    max-width: 22px;
}

.about-info-content{
    width: 100%;
    max-width: calc(100% - 55px);
}

.about-info-content p{
    margin-bottom: 2px;
}

.about-info-content h3{
    font-size: 16px;
    line-height: 1.4em;
}

.about-info-content h3 a{
    color: inherit;
    transition: all 0.3s ease-in-out;
}

.about-info-content h3 a:hover{
    color: var(--accent-color);
}

.text-effect .line{
    width: 100%;
    color: var(--dark-divider-color);
    background: linear-gradient(to right, var(--primary-color), var(--primary-color)) no-repeat;
    -webkit-background-clip: text;
    background-clip: text;
    background-size: 0%;
}

.about-us-body{
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 85%;
    margin-top: 80px;
}

.about-us-body .icon-box{
    margin-right: 50px;
}

.about-us-body .icon-box a{
    display: block;
}

.about-us-body .icon-box a img{
    width: 100%;
    max-width: 80px;
    transition: all 0.4s ease-in-out;
    transform: rotate(-45deg);
}

.about-us-body .icon-box a:hover img{
    transform: rotate(0);
}

.about-body-content{
    width: calc(100% - 130px);
}

.about-body-content p{
    margin-bottom: 0;
}

/************************************/
/*** 	 07. Our Services css	  ***/
/************************************/

.our-services{
    padding: 100px 0;
}

.service-item{
    position: relative;
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    height: calc(100% - 30px);
    margin-bottom: 30px;
    padding: 40px 35px;
    overflow: hidden;
}

.service-item::before{
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    border-radius: 999px 999px 0 0;
    width: 100%;
    height: 0%;
    transition: all 0.4s ease-in-out;
}

.service-item:hover:before{
    height: 100%;
    border-radius: 0;
}

.service-item .icon-box{
    position: relative;
    margin-bottom: 130px;
    z-index: 1;
}

.service-item .icon-box img{
    width: 100%;
    max-width: 60px;
}

.service-item-content{
    position: relative;
    z-index: 1;
}

.service-item-content h3{
    font-size: 20px;
    margin-bottom: 20px;
}

.service-item-content h3 a{
    color: inherit;
}

.service-item-content p{
    margin-bottom: 0;
    transition: all 0.3s ease-in-out;
}

.service-item:hover .service-item-content p{
    color: var(--primary-color);
}

.service-contact-box{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    height: calc(100% - 30px);
    margin-bottom: 30px;
    padding: 40px 25px 0;
    overflow: hidden;
    transition: all 0.4s ease-in-out;
}

.service-contact-content{
    margin-left: 15px;
    margin-bottom: 40px;
}

.service-contact-content p{
    margin-bottom: 15px;
}

.service-contact-content h3{
    font-size: 20px;
}

.service-contact-content h3 a{
    display: inline-flex;
    align-items: center;
    gap: 15px;
    color: inherit;
    transition: all 0.3s ease-in-out;
}

.service-contact-content h3 a img{
    width: 100%;
    max-width: 26px;
    transition: all 0.3s ease-in-out;
}

.service-contact-content h3 a:hover{
    color: var(--accent-color);
}

.service-image{
    text-align: center;
    margin-bottom: -1px;
}

.service-image figure{
    display: block;
}

.service-image img{
    width: 100%;
    aspect-ratio: 1 / 1.045;
    object-fit: cover;
}

.section-footer-text{
	margin-top: 30px;
	text-align: center;
}

.section-footer-text span{
	font-weight: 500;
	display: inline-block;
	background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
	background-size: 200% auto;
	color: var(--primary-color);
	line-height: 1.2em;
	padding: 3px 12px;
	border-radius: 99px;
	margin-right: 10px;
	transition: all 0.4s ease-in-out;
}

.section-footer-text p:hover span{
	background-position: right center;
}

.section-footer-text p{
	margin-bottom: 0;
}

.section-footer-text p a{
	font-weight: 700;
	text-transform: capitalize;
	text-decoration: underline;
	text-underline-offset: 3px;
	color: var(--accent-color);
	transition: all 0.3s ease-in-out;
}

.section-footer-text p a:hover{
	color: var(--primary-color);
}

/************************************/
/*** 	08. Why Choose Us  css	  ***/
/************************************/

.why-choose-us{
    padding: 160px 0;
}

.why-choose-image{
    position: relative;
    text-align: center;
    padding: 0 60px;
}

.why-choose-image::before{
    content: '';
    position: absolute;
    top: -25%;
    left: 0;
    right: 0;
    background: url('../images/why-choose-bg-shape.svg') no-repeat;
    background-position: top center;
    background-size: contain;
    width: 100%;
    height: 100%;
}

.why-choose-image figure{
    position: relative;
    display: block;
    z-index: 1;
}

.why-choose-image img{
    width: 100%;
    aspect-ratio: 1 / 1.6;
    object-fit: cover;
    margin-bottom: -160px;
}

.why-choose-list ul{
    display: flex;
    flex-wrap: wrap;
    gap: 20px 30px;
	margin: 0;
	padding: 0;
	list-style: none;
}

.why-choose-list ul li{
	position: relative;
    width: calc(50% - 15px);
	line-height: 1.5em;
    padding-left: 30px;
}

.why-choose-list ul li:before{
    content: '\f058';
    position: absolute;
    font-family: 'Font Awesome 6 Free';
    font-size: 18px;
    font-weight: 900;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    top: 0;
    left: 0;
}

.why-choose-body{
    display: flex;
    flex-wrap: wrap;
    gap: 30px 100px;
    margin-top: 60px;
}

.why-choose-item-list{
    width: calc(100% - 260px);
}

.why-choose-item{
    display: flex;
    align-items: center;
    margin-bottom: 40px;
}

.why-choose-item:last-child{
    margin-bottom: 0;
}

.why-choose-item .icon-box{
    width: 60px;
    height: 60px;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
	background-size: 200% auto;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 20px;
    transition: all 0.4s ease-in-out;
}

.why-choose-item:hover .icon-box{
    background-position: right center;
}

.why-choose-item .icon-box img{
    width: 100%;
    max-width: 30px;
}

.why-choose-item-content{
    width: calc(100% - 80px);
}

.why-choose-item-content h3{
    font-size: 20px;
    line-height: 1.4em;
}

.get-started-circle{
    position: relative;
    align-content: center;
}

.get-started-circle a,
.get-started-circle a figure{
    display: block;
    border-radius: 50%;
    overflow: hidden;
}

.get-started-circle a figure img{
    width: 100%;
    max-width: 160px;
    border-radius: 50%;
    animation: infiniterotate 30s infinite linear;
}

@keyframes infiniterotate{
	0%{
		transform: rotate(0deg);
    }
	100%{
		transform: rotate(360deg);
	}
}

.get-started-circle .circle-arrow{
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.get-started-circle .circle-arrow img{
    width: 100%;
    max-width: 60px;
    transform: rotate(-45deg);
    transition: all 0.3s ease-in-out;
}

.get-started-circle a:hover .circle-arrow img{
    transform: rotate(0);
}

/************************************/
/*** 	  09. How It Work css	  ***/
/************************************/

.how-it-work{
    padding: 100px 0;
}

.how-work-content{
    margin-right: 20px;
}

.how-work-item{
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.how-work-item:last-child{
    margin-bottom: 0;
}

.how-work-item .icon-box{
    position: relative;
    width: 60px;
    height: 60px;
    background-color: var(--secondary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 30px;
    transition: all 0.4s ease-in-out;
}

.how-work-item .icon-box::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    border-radius: 50%;
    width: 100%;
    height: 100%;
    transform: scale(0);
    transition: all 0.4s ease-in-out;
}

.how-work-item:hover .icon-box::before{
    transform: scale(1);
}

.how-work-item .icon-box img{
    position: relative;
    width: 100%;
    max-width: 30px;
    z-index: 1;
}

.how-work-item-content{
    width: calc(100% - 90px);
}

.how-work-item-content h3{
    font-size: 20px;
    line-height: 1.4em;
    margin-bottom: 10px;
}

.how-work-item-content p{
    margin-bottom: 0;
}

.how-work-image{
    position: relative;
    height: 100%;
}

.how-work-image figure{
    display: block;
    border-radius: 12px;
    height: 100%;
}

.how-work-image figure img{
    width: 100%;
    height: 100%;
    aspect-ratio: 1 / 0.53;
    object-fit: cover;
    border-radius: 12px;
}

/************************************/
/*** 	 10. Our Projects css     ***/
/************************************/

.our-projects{
    padding: 160px 0;
}

.project-list-box-1{
    margin-right: 25px;
}

.project-list-box-2{
    margin-top: 80px;
    margin-left: 25px;
}

.project-item{
    display: flex;
    flex-direction: column;
    gap: 40px;
    background-color: var(--secondary-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    margin-bottom: 80px;
    padding: 40px;
}

.project-item:last-child{
    margin-bottom: 0;
}

.project-content p{
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
	line-height: 1.357em;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--primary-color);
	background: var(--divider-color);
	border: 1px solid var(--divider-color);
	border-radius: 100px;
	padding: 9px 24px;
    margin-bottom: 20px;
}

.project-content h3{
    font-size: 20px;
    line-height: 1.4em;
}

.project-content h3 a{
    color: inherit;
}

.project-image a,
.project-image a figure{
    display: block;
    border-radius: 12px;
    overflow: hidden;
    cursor: none;
}

.project-image figure img{
    width: 100%;
    aspect-ratio: 1 / 0.68;
    object-fit: cover;
    transition: all 0.4s ease-in-out;
}

.project-item:hover .project-image figure img{
    transform: scale(1.05);
}

/************************************/
/***   11. Our Testimonials css   ***/
/************************************/

.our-testimonials{
    padding: 100px 0;
}

.our-testimonial-content{
    margin-right: 15px;
}

.satisfy-client-box{
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 100px;
}

.satisfy-client-images{
    display: inline-flex;
}

.satisfy-client-image{
    margin-left: -14px;
}

.satisfy-client-image:first-child{
    margin: 0;
}

.satisfy-client-image figure{
    display: block;
    border-radius: 50%;
}

.satisfy-client-image img{
    width: 100%;
    max-width: 70px;
    border-radius: 50%;
    border: 1px solid var(--bg-color);
}

.satisfy-client-content{
    width: 45%;
}

.satisfy-client-content p{
    margin-bottom: 0;
}

.satisfy-client-content p span{
    font-weight: 700;
    color: var(--accent-color);
}

.testimonial-slider{
    margin-left: 100px;
}

.testimonial-slider .swiper-wrapper{
    cursor: none;
}

.testimonial-item{
    background: var(--secondary-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    padding: 40px;
}

.testimonial-content{
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: 100px;
    margin-bottom: 30px;
}

.testimonial-content h3{
    font-size: 20px;
    margin-bottom: 20px;
}

.testimonial-content p{
    margin-bottom: 0;
}

.testimonial-author{
	display: flex;
	align-items: center;
}

.author-image{
	margin-right: 15px;
}

.author-image figure{
	display: block;
	border-radius: 50%;
}

.author-image img{
	max-width: 60px;
	border-radius: 50%;
}

.author-content{
	width: calc(100% - 75px);
}

.author-content h3{
	font-size: 20px;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.author-content p{
    color: var(--primary-color);
	text-transform: capitalize;
	margin: 0;
}

.testimonial-btn{
	display: flex;
	align-items: center;
    justify-content: center;
	margin-top: 60px;
}

.testimonial-slider .testimonial-button-next,
.testimonial-slider .testimonial-button-prev{
	position: relative;
	width: 60px;
	height: 60px;
	border: 1px solid var(--divider-color);
	border-radius: 50%;
	transition: all 0.4s ease-in-out;
}

.testimonial-slider .testimonial-button-next{
	margin-left: 20px;
}

.testimonial-slider .testimonial-button-next:hover,
.testimonial-slider .testimonial-button-prev:hover{
	background: var(--accent-color);
}

.testimonial-slider .testimonial-button-next::before,
.testimonial-slider .testimonial-button-prev::before{
    content: '';
	position: absolute;
    top: 50%;
    bottom: 0;
    left: 50%;
    right: 0;
	transform: translate(-50%, -50%) rotate(45deg);
	background: url('../images/arrow-white.svg') no-repeat;
	background-position: center center;
	background-size: cover;
	width: 16px;
	height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonial-slider .testimonial-button-prev::before{
	transform: translate(-50%, -50%) rotate(225deg);
}

/************************************/
/*** 	   12. Our Faqs css		  ***/
/************************************/

.our-faqs{
    padding: 160px 0;
}

.faq-accordion .accordion-item{
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
	border-radius: 12px;
    padding: 0;
	margin-bottom: 30px;
	transition: all 0.3s ease-in-out;
	overflow: hidden;
}

.faq-accordion .accordion-item:last-child{
	margin-bottom: 0;
}

.faq-accordion .accordion-header .accordion-button{
	font-size: 18px;
	font-weight: 600;
	line-height: 1.4em;
	color: var(--primary-color);
	padding: 20px 50px 20px 20px;
	transition: all 0.3s ease-in-out;
}

.faq-accordion .accordion-item .accordion-button::after,
.faq-accordion .accordion-item .accordion-button.collapsed::after{
    content: '\f068';
    font-family: 'FontAwesome';
    position: absolute;
    right: 20px;
    top: 50%;
	transform: translateY(-50%);    
    font-size: 18px;
    color: var(--primary-color);
    transition: all 0.3s ease-in-out;
}

.faq-accordion .accordion-item .accordion-button.collapsed::after{
    content: '\2b';
}

.faq-accordion .accordion-item .accordion-body{
	background: transparent;
	padding: 0 50px 20px 20px;
}

.faq-accordion .accordion-item .accordion-body p{
	margin: 0;
}

/************************************/
/*** 	    13. CTA Box css		  ***/
/************************************/

.cta-box{
    padding: 30px 0;
}

.cta-box-btn{
    position: relative;
    display: inline-block;
}

.cta-box-btn::before{
	content: '';
    position: absolute;
    bottom: -10px;
    right: -40px;
    transform: translateX(100%);
    background: url('../images/cta-body-arrow.svg');
    background-repeat: no-repeat;
    background-position: left bottom;
    background-size: cover;
    width: 85px;
    height: 85px;
    z-index: -1;
    animation: ctaarrowmove 2s infinite linear alternate;
}

@keyframes ctaarrowmove{
	50%{
		right: -70px;
	}
}

.cta-box-image{
    position: relative;
    padding: 0 20px;
}

.cta-box-image::before{
    content: '';
    position: absolute;
    top: -60px;
    right: 0;
    left: 0;
    background: url('../images/cta-box-image-bg.svg') no-repeat;
    background-position: top center;
    background-size: cover;
    width: 100%;
    height: 100%;
}

.cta-box-image figure{
    position: relative;
    display: block;
    z-index: 1;
}

.cta-box-image img{
    width: 100%;
    aspect-ratio: 1 / 1.18;
    object-fit: cover;
    margin-bottom: -30px;
}

/************************************/
/*** 	   14. Our Blog css		  ***/
/************************************/

.our-blog{
    padding: 160px 0 130px;
}

.post-item{
	background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
	height: calc(100% - 30px);
	margin-bottom: 30px;
    padding: 30px;
}

.post-featured-image,
.post-item-content{
    margin-bottom: 20px;
}

.post-featured-image a{
	display: block;
    border-radius: 12px;
    overflow: hidden;
    cursor: none;	
}

.post-featured-image figure{
	display: block;
}

.post-featured-image img{
	width: 100%;
    aspect-ratio: 1 / 0.735;
    object-fit: cover;
    transition: all 0.5s ease-in-out;
}

.post-item:hover .post-featured-image img{
	transform: scale(1.1);
}

.post-item-content h2{
    font-size: 20px;
    line-height: 1.4em;
}

.post-item-content h2 a{
    color: inherit;
}

/************************************/
/*** 	    15. Footer css		  ***/
/************************************/

.main-footer{
    padding: 100px 0 0;
    margin-bottom: 100px;
}

.footer-header{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    background: var(--divider-color);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    padding: 35px 40px;
    margin-bottom: 80px;
}

.footer-header-title h2{
    font-size: 70px;
    text-transform: uppercase;
    cursor: none;
}

.footer-header-title h2 span{
    color: var(--secondary-color);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-stroke: 1px var(--primary-color);
}

.footer-header .hero-btn .btn-default{
    height: 80px;
    width: 80px;
}

.footer-header .hero-btn .btn-default img{
    max-width: 45px;
}

.footer-contact-box{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-right: 50px;
}

.footer-contact-box .footer-links{
    width: calc(50% - 15px);
}

.footer-links h3{
    font-size: 20px;
    text-transform: capitalize;
    margin-bottom: 20px;
}

.footer-links ul{
    margin: 0;
	padding: 0;
	list-style: none;
}

.footer-links ul li{
	line-height: 1.6em;
	margin-bottom: 5px;
}

.footer-links ul li:last-child{
	margin-bottom: 0;
}

.footer-links ul li b{
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.footer-links ul li a{
    color: inherit;
	transition: all 0.3s ease-in-out;
}

.footer-links ul li a:hover{
    color: var(--accent-color);
}

.footer-social-links ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.footer-social-links ul li{
    display: inline-block;
	margin-right: 10px;
}

.footer-social-links ul li:last-child{
    margin: 0;
}

.footer-social-links ul li a{
    border: 1px solid var(--divider-color);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease-in-out;
}

.footer-social-links ul li a:hover{
	background: var(--primary-color);
}

.footer-social-links ul li a i{
    font-size: 18px;
    line-height: normal;
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--accent-secondary-color) 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.footer-newsletter-form .form-group{
    position: relative;
    display: flex;
}

.footer-newsletter-form .form-group .form-control{
	width: 100%;
	font-size: 16px;
	font-weight: 400;
	line-height: 1.2em;
	color: var(--text-color);
	background: transparent;
	border: none;
	border-bottom: 1px solid var(--divider-color);
	border-radius: 0;
	outline: none;
	box-shadow: none;
	padding: 0 30px 16px 0;
}

.footer-newsletter-form .form-group .form-control::placeholder{
	color: var(--text-color);
}

.footer-newsletter-form .form-group .newsletter-btn{
    position: absolute;
    top: 0;
    right: 0;
    background: transparent;
	border: none;
	border-radius: 0;
    padding: 0;
	transition: all 0.3s ease-in-out;
}

.footer-newsletter-form .form-group .newsletter-btn img{
	width: 16px;
	transition: all 0.3s ease-in-out;
}

.footer-newsletter-form .form-group .newsletter-btn:hover img{
    transform: rotate(45deg);
}

.footer-copyright{
    border-top: 1px solid var(--divider-color);
    padding: 60px 0;
    margin-top: 60px;
}

.footer-copyright-text{
    text-align: end;
}

.footer-copyright-text p{
    margin-bottom: 0;
}

/************************************/
/*** 	16. About Us Page css     ***/
/************************************/

.page-header{
	background: url('../images/page-header-bg.png') no-repeat;
	background-repeat: no-repeat;
	background-position: top center;
	background-size: cover;
	padding: 215px 0 100px;
}

.page-header-box{
    text-align: center;
}

.page-header-box h1{
    display: inline-block;
	font-size: 70px;
	line-height: 1.3em;
	font-weight: 300;
    letter-spacing: -0.03em;
	margin-bottom: 10px;
	cursor: none;
}

.page-header-box ol{
	margin: 0;
	padding: 0;
	justify-content: center;
}

.page-header-box ol li.breadcrumb-item{
	font-size: 18px;
	text-transform: capitalize;
	line-height: normal;
	color: var(--text-color);
}

.page-header-box ol li.breadcrumb-item a{
    color: inherit;
}

.page-header-box ol .breadcrumb-item+.breadcrumb-item::before{
    color: var(--text-color);
}

.our-scrolling-ticker.subpages-scrolling-ticker{
    padding: 20px 0;
}

.our-approach{
    padding: 100px 0;
}

.our-approach-content{
    position: sticky;
    top: 20px;
    margin-right: 15px;
}

.our-approach-content .section-title{
    margin-bottom: 0;
}

.approach-item-box{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.approach-item{
    position: relative;
    width: calc(50% - 15px);
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 40px 35px;
    overflow: hidden;
}

.approach-item::before{
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    border-radius: 999px 999px 0 0;
    width: 100%;
    height: 0%;
    transition: all 0.4s ease-in-out;
}

.approach-item:hover::before{
    height: 100%;
    border-radius: 0;
}

.approach-item-content,
.approach-item .icon-box,
.approach-item-image{
    position: relative;
    z-index: 1;
}

.approach-item-content,
.approach-item .icon-box{
    margin-bottom: 30px;
}

.approach-item-content h3{
    font-size: 20px;
    margin-bottom: 20px;
}

.approach-item-content p{
    margin-bottom: 0;
    transition: all 0.4s ease-in-out;
}

.approach-item:hover .approach-item-content p{
    color: var(--primary-color);
}

.approach-item .icon-box img{
    width: 100%;
    max-width: 60px;
}

.approach-item-image figure{
    display: block;
    border-radius: 12px;
}

.approach-item-image img{
    width: 100%;
    aspect-ratio: 1 / 0.68;
    object-fit: cover;
    border-radius: 12px;
}

.our-values{
    padding: 160px 0;
}

.value-box-list{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.value-box-item{
    width: calc(50% - 15px);
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 40px 35px;
    overflow: hidden;
}

.value-box-header{
    margin-bottom: 80px;
}

.value-box-header h2{
    display: inline-block;
    font-size: 48px;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
	background-size: 200% auto;
    background-clip: text;
    -webkit-text-fill-color: transparent;
	transition: all 0.4s ease-in-out;
    margin-bottom: 10px;
}

.value-box-item:hover .value-box-header h2{
	background-position: right center;
}

.value-box-header p{
    color: var(--primary-color);
    margin-bottom: 0;
}

.value-box-body p{
    margin-bottom: 20px;
}

.value-box-body ul{
	margin: 0;
	padding: 0;
	list-style: none;
}

.value-box-body ul li{
    position: relative;
	line-height: 1.5em;
    padding-left: 30px;
    margin-bottom: 15px;
}

.value-box-body ul li:last-child{
    margin-bottom: 0;
}

.value-box-body ul li:before{
    content: '\f058';
    position: absolute;
    font-family: 'Font Awesome 6 Free';
    font-size: 18px;
    font-weight: 900;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    top: 0;
    left: 0;
}

.value-image{
    margin-left: 15px;
}

.value-image figure{
    display: block;
    border-radius: 12px;
}

.value-image img{
    width: 100%;
    aspect-ratio: 1 / 1.04;
    object-fit: cover;
    border-radius: 12px;
}

.what-drives-us{
    padding: 100px 0;
}

.what-drives-image{
    height: 100%;
}

.what-drives-image figure{
    display: block;
    height: 100%;
    border-radius: 12px;
}

.what-drives-image img{
    width: 100%;
    height: 100%;
    aspect-ratio: 1 / 0.55;
    object-fit: cover;
}

.what-drives-content{
    height: 100%;
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    padding: 40px;
}

.what-drives-list ul{
	margin: 0;
	padding: 0;
	list-style: none;
}

.what-drives-list ul li{
    position: relative;
	line-height: 1.5em;
    padding-left: 30px;
    margin-bottom: 15px;
}

.what-drives-list ul li:last-child{
    margin-bottom: 0;
}

.what-drives-list ul li:before{
    content: '\f058';
    position: absolute;
    font-family: 'Font Awesome 6 Free';
    font-size: 18px;
    font-weight: 900;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    top: 0;
    left: 0;
}

.who-we-are{
    padding: 160px 0;
}

.brand-logo-list{
    display: flex;
    flex-wrap: wrap;
    border: 1px solid var(--divider-color);
    border-right: none;
    border-bottom: none;
}

.brand-logo-item{
    width: 20%;
    padding: 45px 30px;
    border-right: 1px solid var(--divider-color);
    border-bottom: 1px solid var(--divider-color);
    border-top: none;
}

.brand-logo-item figure{
    display: block;
}

.brand-logo-item img{
    width: 100%;
    height: 40px;
}

.our-team{
	padding: 160px 0 130px;
}

.team-item{
    text-align: center;
    height: calc(100% - 30px);
    margin-bottom: 30px;
}

.team-image{
	background-color: var(--secondary-color);
    border: 1px solid var(--divider-color);
	border-radius: 12px;
	text-align: center;
	padding: 40px 25px 0;
	margin-bottom: 20px;
}

.team-image a{
    display: block;
    cursor: none;
}

.team-image figure{
    display: block;
}

.team-image img{
    width: 100%;
	max-width: 243px;
}

.team-content{
    margin-bottom: 15px;
}

.team-content h3{
    font-size: 20px;
    line-height: 1.4em;
}

.team-content h3 a{
    color: inherit;
}

.team-social-icon ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.team-social-icon ul li{
	display: inline-block;
	margin-right: 10px;
}

.team-social-icon ul li:last-child{
	margin-right: 0;
}

.team-social-icon ul li a{
	background: var(--divider-color);
    border: 1px solid var(--divider-color);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease-in-out;
}

.team-social-icon ul li a:hover{
	background: var(--primary-color);
}

.team-social-icon ul li a i{
	font-size: 18px;
    line-height: normal;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/************************************/
/*** 	 17. Services Page css	  ***/
/************************************/

.page-services{
    padding: 160px 0 130px;
}

/************************************/
/*** 	18. Service Single css    ***/
/************************************/

.page-service-single{
    padding: 160px 0;
}

.page-single-sidebar{
    position: sticky;
    top: 30px;
    margin-right: 15px;
}

.page-catagery-list{
    background: var(--secondary-color);
	border: 1px solid var(--divider-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    margin-bottom: 60px;
    overflow: hidden;
}

.page-catagery-list h3{
	font-size: 20px;
    text-transform: capitalize;
    color: var(--primary-color);
    border-bottom: 1px solid var(--divider-color);
    padding: 30px;
}

.page-catagery-list ul{
    list-style: none;
    margin: 0;
    padding: 30px;
}

.page-catagery-list ul li{
    border-bottom: 1px solid var(--divider-color);
    line-height: 1.5em;
    padding-bottom: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease-in-out;
}

.page-catagery-list ul li:last-child{
    margin: 0;
    padding: 0;
    border-bottom: none;
}

.page-catagery-list ul li a{
    position: relative;
	display: block;
    text-transform: capitalize;
    color: inherit;
	padding-right: 30px;
    transition: all 0.3s ease-in-out;
}

.page-catagery-list ul li a:hover{
    color: var(--primary-color);
}

.page-catagery-list ul li a::before{
	content: '';
    position: absolute;
	right: 0;
	top: 50%;
    background: url('../images/arrow-gradiant.svg') no-repeat;
	background-position: center center;
	background-size: cover;
	width: 14px;
	height: 14px;
    transform: translateY(-50%);
	transition: all 0.3s ease-in-out;
}

.page-catagery-list ul li a:hover:before{
	filter: brightness(0) invert(1);
	transform: translateY(-50%) rotate(45deg);
}

.service-contact-box.sidebar-cta-box{
    height: auto;
    text-align: center;
    padding-top: 40px;
    margin-bottom: 0;
}

.service-contact-box.sidebar-cta-box .service-contact-content{
    margin-left: 0;
}

.service-contact-box.sidebar-cta-box .service-image img{
    max-width: 270px;
}

.page-single-image{
    margin-bottom: 40px;
}

.page-single-image figure{
    display: block;
    border-radius: 12px;
}

.page-single-image img{
    width: 100%;
    aspect-ratio: 1 / 0.581;
    object-fit: cover;
    border-radius: 12px;
}

.service-entry{
    margin-bottom: 60px;
}

.service-entry p{
    margin-bottom: 20px;
}

.service-entry p:last-child{
    margin-bottom: 0;
}

.service-entry h2{
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 20px;
}

.service-entry ul{
    display: flex;
    flex-wrap: wrap;
    gap: 15px 30px;
	margin: 0;
	padding: 0;
	list-style: none;
}

.service-entry ul li{
	position: relative;
    width: calc(50% - 15px);
	line-height: 1.5em;
    padding-left: 30px;
}

.service-entry ul li:before{
    content: '\f058';
    position: absolute;
    font-family: 'Font Awesome 6 Free';
    font-size: 18px;
    font-weight: 900;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    top: 0;
    left: 0;
}

.data-driven-box,
.service-decision-box,
.service-insight-box{
    margin-top: 60px;
}

.data-driven-box ul{
    margin: 40px 0;
}

.data-driven-item-list{
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    padding: 30px;
}

.data-driven-item{
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid var(--divider-color);
    margin-bottom: 40px;
    padding-bottom: 40px;
}

.data-driven-item:last-child{
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.data-driven-item .icon-box{
    width: 60px;
    height: 60px;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
	background-size: 200% auto;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 30px;
    transition: all 0.4s ease-in-out;
}

.data-driven-item:hover .icon-box{
    background-position: right center;
}

.data-driven-item .icon-box img{
    width: 100%;
    max-width: 30px;
}

.data-driven-item-content{
    width: calc(100% - 90px);
}

.data-driven-item-content h3{
    font-size: 20px;
    margin-bottom: 15px;
}

.service-decision-image-content{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 40px 0;
}

.service-decision-item,
.service-decision-image{
    width: calc(50% - 15px);
}

.service-decision-item{
    background: var(--secondary-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    padding: 30px;
}

.service-decision-item .icon-box{
    margin-bottom: 40px;
}

.service-decision-item .icon-box img{
    width: 100%;
    max-width: 60px;
}

.service-decision-item-content h3{
    font-size: 20px;
    margin-bottom: 15px;
}

.service-decision-image figure{
    display: block;
    height: 100%;
    border-radius: 12px;
}

.service-decision-image img{
    width: 100%;
    height: 100%;
    aspect-ratio: 1 / 0.68;
    object-fit: cover;
    border-radius: 12px;
}

.service-insight-images{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 40px 0;
}

.service-insight-img{
    width: calc(50% - 15px);
}

.service-insight-img figure{
    display: block;
    border-radius: 12px;
}

.service-insight-img img{
    width: 100%;
    aspect-ratio: 1 / 0.98;
    object-fit: cover;
    border-radius: 12px;
}

/************************************/
/*** 	 19. Blog Archive css	  ***/
/************************************/

.page-blog{
	padding: 160px 0;
}

.page-pagination{
    margin-top: 20px;
    text-align: center;
}

.page-pagination ul{
    justify-content: center;
    padding: 0;
    margin: 0;
}

.page-pagination ul li a,
.page-pagination ul li span{
    display: flex;
    text-decoration: none;
    justify-content: center;
    align-items: center;
    background: var(--divider-color);
    color: var(--primary-color);
	border-radius: 10px;
    width: 40px;
    height: 40px;
    margin: 0 5px;
    font-weight: 600;
	line-height: 1em;
    transition: all 0.3s ease-in-out;
}

.page-pagination ul li.active a, 
.page-pagination ul li a:hover{
    background: var(--accent-color);
}

/************************************/
/*** 	 20. Blog Single css      ***/
/************************************/

.page-single-post{
	padding: 160px 0;
}

.post-single-meta ol li{
	font-size: 18px;
	color: var(--primary-color);
	margin-right: 15px;
}

.post-single-meta ol li:last-child{
	margin-right: 0;
}

.post-single-meta ol li i{
    font-size: 18px;
    color: var(--primary-color);
    margin-right: 5px;
}

.post-image{
	position: relative;
	margin-bottom: 30px;
}

.post-image figure{
	display: block;	
	border-radius: 12px;
	overflow: hidden;
}

.post-image img{
	width: 100%;
	aspect-ratio: 1 / 0.50;
	object-fit: cover;
	border-radius: 12px;
}

.post-content{
	width: 100%;
	max-width: 1100px;
	margin: 0 auto;
}

.post-entry{
	border-bottom: 1px solid var(--divider-color);
	padding-bottom: 30px;
    margin-bottom: 30px;
}

.post-entry:after{
    content: '';
    display: block;
    clear: both;
}

.post-entry a{
    color: var(--accent-color);
}

.post-entry h1,
.post-entry h2,
.post-entry h3,
.post-entry h4,
.post-entry h5,
.post-entry h6{
	font-weight: 600;
	line-height: 1.2em;
	margin: 0 0 0.417em;
}

.post-entry h1{
	font-size: 70px;
}

.post-entry h2{
	font-size: 48px;
}

.post-entry h3{
	font-size: 40px;
}

.post-entry h4{
	font-size: 30px;
}

.post-entry h5{
	font-size: 24px;
}

.post-entry h6{
	font-size: 20px;
}

.post-entry p{
	margin-bottom: 20px;
}

.post-entry p:last-child{
	margin-bottom: 0;
}

.post-entry p strong{
	color: var(--primary-color);
	font-size: 18px;
	font-weight: 600;
}

.post-entry ol{
    margin: 0 0 30px;
}

.post-entry ul{
	padding: 0;
	margin: 20px 0 20px;
	padding-left: 20px;
}

.post-entry ol li,
.post-entry ul li{
    position: relative;
	font-size: 16px;
    font-weight: 500;
    line-height: 1.5em;
    color: var(--text-color);
    margin-bottom: 15px;
}

.post-entry ul li:last-child{
	margin-bottom: 0;
}

.post-entry ul ul,
.post-entry ul ol,
.post-entry ol ol,
.post-entry ol ul{
    margin-top: 20px;
    margin-bottom: 0;
}

.post-entry ul ul li:last-child,
.post-entry ul ol li:last-child,
.post-entry ol ol li:last-child,
.post-entry ol ul li:last-child{
    margin-bottom: 0;
}

.post-entry blockquote{
	background: url('../images/icon-blockquote.svg'), var(--secondary-color);
	background-repeat: no-repeat;
	background-position: 30px 30px;
    background-size: 45px;
    border: 1px solid var(--divider-color);
	border-radius: 20px;
    padding: 30px 30px 30px 90px;
    margin-bottom: 30px;
}

.post-entry blockquote p{
	font-size: 20px;
	font-weight: 700;
	line-height: 1.5em;
    color: var(--primary-color);
}

.post-entry blockquote p:last-child{
	margin-bottom: 0;
}

.tag-links{
    font-size: 20px;
	font-weight: 700;
    text-transform: capitalize;
	color: var(--primary-color);
	display: inline-flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 15px;
}

.post-tags .tag-links a{
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
    line-height: 1em;
	background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
    background-size: 200% auto;
    color: var(--white-color);
	border-radius: 100px;
    padding: 12px 20px;
	transition: all 0.3s ease-in-out;
}

.post-tags .tag-links a:hover{
	background-position: right center;
}

.post-social-sharing{
    text-align: right;
}

.post-social-sharing ul{
    list-style: none;
    padding: 0;
    margin: 0;
}

.post-social-sharing ul li{
    display: inline-block;
    margin-right: 10px;
}

.post-social-sharing ul li:last-child{
	margin-right: 0;
}

.post-social-sharing ul li a{
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
	background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
    background-size: 200% auto;
    color: var(--primary-color);
	border-radius: 100px;
    width: 40px;
    height: 40px;
    transition: all 0.3s ease-in-out;
}

.post-social-sharing ul li:hover a{
    background-position: right center;
}

.post-social-sharing ul li a i{
    font-size: 18px;
    color: inherit;
}

/************************************/
/*** 	 21. Projects Page css	  ***/
/************************************/

.page-projects{
    padding: 160px 0;
}

.page-project-list{
    display: flex;
    flex-wrap: wrap;
    gap: 80px;
}

.page-project-list .project-item{
    width: calc(50% - 40px);
    margin: 0;
}

/************************************/
/*** 	22. Project Single css    ***/
/************************************/

.page-project-single{
    padding: 160px 0;
}

.project-category-list h3{
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    border: none;
}

.project-category-list ul li{
    display: flex;
    justify-content: space-between;
    color: var(--primary-color);
    font-weight: 700;
}

.project-category-list ul li span{
    width: 55%;
    font-weight: 400;
}

.project-entry{
    margin-bottom: 60px;
}

.project-entry p{
    margin-bottom: 20px;
}

.project-entry p:last-child{
    margin-bottom: 0;
}

.project-entry h2{
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 20px;
}

.project-entry ul{
    display: flex;
    flex-wrap: wrap;
    gap: 15px 30px;
	margin: 0;
	padding: 0;
	list-style: none;
}

.project-entry ul li{
	position: relative;
    width: calc(50% - 15px);
	line-height: 1.5em;
    padding-left: 30px;
}

.project-entry ul li:before{
    content: '\f058';
    position: absolute;
    font-family: 'Font Awesome 6 Free';
    font-size: 18px;
    font-weight: 900;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    top: 0;
    left: 0;
}

.project-integration-box,
.project-growth-box,
.project-demand-box{
    margin-top: 60px;
}

.project-integration-item-list{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 40px 0;
}

.project-integration-item{
    width: calc(50% - 15px);
}

.project-integration-item .why-choose-item{
    margin-bottom: 30px;
}

.project-integration-image figure{
    display: block;
    border-radius: 12px;
}

.project-integration-image img{
    width: 100%;
    aspect-ratio: 1 / 0.613;
    object-fit: cover;
    border-radius: 12px;
}

.project-growth-content-box{
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 40px;
    margin-top: 40px;
}

.project-growth-item-list{
    display: flex;
    flex-wrap: wrap;
    gap: 30px 50px;
}

.project-growth-item-list .how-work-item{
    width: calc(33.33% - 33.33px);
    display: block;
    margin: 0;
}

.project-growth-item-list .how-work-item .icon-box{
    margin: 0 0 40px;
}

.project-growth-item-list .how-work-item-content{
    width: 100%;
}

.project-growth-list{
    border-top: 1px solid var(--divider-color);
    padding-top: 40px;
    margin-top: 40px;
}

.project-demand-item-box{
    border: 1px solid var(--divider-color);
    background: var(--secondary-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 40px;
    margin-top: 40px;
}

.project-demand-list-item{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 30px;
    border-bottom: 1px solid var(--divider-color);
    margin-bottom: 40px;
    padding-bottom: 40px;
}

.project-demand-list-item:last-child{
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.project-demand-list-item:nth-child(even){
    flex-direction: row-reverse;
}

.project-demand-image{
    width: calc(37% - 15px);
}

.project-demand-image figure{
    display: block;
    border-radius: 12px;
}

.project-demand-image img{
    width: 100%;
    aspect-ratio: 1 / 0.95;
    object-fit: cover;
    border-radius: 12px;
}

.project-demand-content{
    width: calc(63% - 15px);
}

.project-demand-content ul li{
    width: 100%;
}

/************************************/
/*** 	  23. Team Page css	      ***/
/************************************/

.page-team{
    padding: 160px 0 130px;
}

/************************************/
/***  	 24. Team Single css      ***/
/************************************/

.page-team-single{
    padding: 160px 0;
}

.team-single-image{
    text-align: center;
    padding: 40px 30px 0;
}

.team-single-image figure{
    display: block;
}

.team-single-image img{
    width: 100%;
    max-width: 320px;
}

.team-member-about,
.team-member-experience,
.team-member-skills{
    margin-bottom: 60px;
}

.member-contact-list{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.member-contact-box{
    width: calc(50% - 15px);
    display: flex;
    background: var(--secondary-color);
    border: 1px solid var(--divider-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: b(20px);
    border-radius: 12px;
    padding: 30px;
}

.member-contact-box .icon-box{
    width: 60px;
	height: 60px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
	background-size: 200% auto;
	border-radius: 50%;
    margin-right: 30px;
	transition: all 0.3s ease-in-out;
}

.member-contact-box:hover .icon-box{
	background-position: right center;
}

.member-contact-box .icon-box img{
	width: 100%;
	max-width: 30px;
}

.member-contact-content{
    width: calc(100% - 90px);
}

.member-contact-content h3{
    font-size: 20px;
    text-transform: capitalize;
    margin-bottom: 10px;
}

.member-contact-content p{
    margin-bottom: 0;
}

.member-social-list{
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    align-items: center;
    margin-top: 40px;
}

.member-social-list h3{
    font-size: 20px;
}

.member-social-list ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.member-social-list ul li{
    display: inline-block;
	margin-right: 10px;
}

.member-social-list ul li:last-child{
	margin-right: 0;
}

.member-social-list ul li a{
	background: var(--divider-color);
    border: 1px solid var(--divider-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease-in-out;
}

.member-social-list ul li a:hover{
	background: var(--primary-color);
}

.member-social-list ul li a i{
	font-size: 20px;
    line-height: normal;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.team-experience-list ul{
    list-style: none;
    padding: 0;
    margin: 0;
}

.team-experience-list ul li{
    position: relative;
	line-height: 1.5em;
    padding-left: 30px;
    margin-bottom: 15px;
}

.team-experience-list ul li:last-child{
    margin-bottom: 0;
}

.team-experience-list ul li:before{
    content: '\f058';
    position: absolute;
    font-family: 'Font Awesome 6 Free';
    font-size: 18px;
    font-weight: 900;
    background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    top: 0;
    left: 0;
}

.member-skill-list{
	display: flex;
	flex-wrap: wrap;
	gap: 40px 30px;
}

.skills-progress-bar{
	width: calc(50% - 15px);
}

.skills-progress-bar .skill-data{
	display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.skills-progress-bar .skillbar .skill-progress{
	position: relative;
	width: 100%;
	height: 16px;
    border: 1px solid var(--divider-color);
	background: var(--secondary-color);
	border-radius: 100px;
    overflow: hidden;
}

.skills-progress-bar .skillbar .skill-progress .count-bar{
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	background: linear-gradient(90deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
	border-radius: 100px;
}

/************************************/
/***  25. Testimonials Page css	  ***/
/************************************/

.page-testimonials{
    padding: 160px 0 130px;
}

.page-testimonials .testimonial-item{
    position: relative;
    height: calc(100% - 30px);
    margin-bottom: 30px;
    overflow: hidden;
}

.page-testimonials .testimonial-item::before{
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(136.83deg, var(--accent-color) 2.01%, var(--accent-secondary-color) 97.82%);
    border-radius: 999px 999px 0 0;
    height: 0;
    width: 100%;
    transition: all 0.4s ease-in-out;
    z-index: 0;
}

.page-testimonials .testimonial-item.active:before,
.page-testimonials .testimonial-item:hover:before{
	border-radius: 0;
	height: 100%;
}

.page-testimonials .testimonial-item .testimonial-author,
.page-testimonials .testimonial-item .testimonial-content{
    position: relative;
    z-index: 1;
}

.page-testimonials .testimonial-item .testimonial-content p{
    transition: all 0.4s ease-in-out;
}

.page-testimonials .testimonial-item.active .testimonial-content p,
.page-testimonials .testimonial-item:hover .testimonial-content p{
    color: var(--primary-color);
}

/************************************/
/*** 	26. Image Gallery css     ***/
/************************************/

.page-gallery{
	padding: 160px 0 130px;
}

.page-gallery-box .photo-gallery{
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.page-gallery-box .photo-gallery a{
	cursor: none;
}

.page-gallery-box .photo-gallery figure{
	display: block;
	border-radius: 12px;
}

.page-gallery-box .photo-gallery img{
	width: 100%;
	aspect-ratio: 1 / 0.829;
	object-fit: cover;
	border-radius: 12px;
}

/************************************/
/*** 	 27. Video Gallery css	  ***/
/************************************/

.page-video-gallery{
	padding: 160px 0 130px;
}

.video-gallery-image{
	height: calc(100% - 30px);
	margin-bottom: 30px;
	overflow: hidden;
}

.video-gallery-image a{
	position: relative;
	display: block;
	cursor: none;
}

.video-gallery-image a::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-color);
	border-radius: 12px;
    opacity: 0%;
    visibility: hidden;
    width: 100%;
    height: 100%;
    z-index: 1;
    transform: scale(0);
    transition: all 0.4s ease-in-out;
}

.video-gallery-image:hover a::before{
    opacity: 40%;
    visibility: visible;
    transform: scale(1);
}

.video-gallery-image a::after{
    content: '\f04b';
	font-family: 'FontAwesome';
    position: absolute;
    top: 50%;
    left: 50%;
    right: 0;
    transform: translate(-50%, -50%);
	font-size: 20px;
	background: linear-gradient(90deg, var(--accent-secondary-color) 2.01%, var(--accent-color) 97.82%);
	color: var(--primary-color);
    border-radius: 50%;
    height: 60px;
    width: 60px;
    cursor: none;
	display: flex;
	align-items: center;
	justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease-in-out;
    z-index: 1;
}

.video-gallery-image:hover a::after{
    opacity: 1;
    visibility: visible;
}

.video-gallery-image img{
	width: 100%;
	aspect-ratio: 1 / 0.829;
	object-fit: cover;
	border-radius: 12px;
}

/************************************/
/*** 	  28. FAQs Page css       ***/
/************************************/

.page-faqs{
	padding: 160px 0;
}

.page-faqs .page-faq-accordion{
    margin-bottom: 60px;
}

.page-faqs .page-faq-accordion:last-child{
    margin-bottom: 0px;
}

/************************************/
/***    29. Contact Us Page css	  ***/
/************************************/

.page-contact-us{
    padding: 160px 0;
}

.contact-us-content{
    margin-right: 15px;
}

.contact-info-body{
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
}

.contact-info-item{
    display: flex;
}

.contact-info-item .icon-box{
    height: 60px;
    width: 60px;
    background: linear-gradient(to right, var(--accent-secondary-color) 0%, var(--accent-color) 50%, var(--accent-secondary-color) 100%);
    background-size: 200% auto;
    border-radius: 100px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-right: 30px;
    transition: all 0.4s ease-in-out;
}

.contact-info-item:hover .icon-box{
    background-position: right center;
}

.contact-info-item .icon-box img{
    width: 100%;
    max-width: 30px;
}

.contact-item-content{
    width: calc(100% - 90px);
}

.contact-item-content h3{
    font-size: 20px;
    margin-bottom: 10px;
}

.contact-item-content p{
    margin-bottom: 0;
}

.contact-item-content p a{
    color: inherit;
    transition: all 0.3s ease-in-out;
}

.contact-item-content p a:hover{
    color: var(--accent-color);
}

.contact-info-item.location-item{
    width: 100%;
    border-top: 1px solid var(--divider-color);
    padding-top: 40px;
}

.contact-us-form{
    background: var(--secondary-color) url('../images/contact-form-bg-shape.svg') no-repeat;
    background-position: center center;
    background-size: cover;
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    padding: 40px;
}

.contact-form form .form-control{
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5em;
    color: var(--text-color);
    background-color: var(--secondary-color);
    border: 1px solid var(--divider-color);
    border-radius: 12px;
    padding: 17px 20px;
    outline: none;
    box-shadow: none;
}

.contact-form form .form-control::placeholder{
    color: var(--text-color);
}

.contact-form form .btn-default{
    width: 100%;
	padding: 17px;
}

.contact-form form .btn-default::before{
    display: none;
}

.google-map-iframe{
    margin-top: 160px;
}

.google-map-iframe,
.google-map-iframe iframe{
    width: 100%;
    height: 570px;
    border-radius: 12px;
}

.google-map-iframe iframe{
    filter: grayscale(1);
    transition: all 0.3s ease-in-out;
}

.google-map-iframe iframe:hover{
    filter: grayscale(0);
}

/************************************/
/*** 	30. 404 Error Page css    ***/
/************************************/

.error-page{
	padding: 160px 0;
}

.error-page-image{
	text-align: center;
	margin-bottom: 30px;
}

.error-page-image img{
	width: 100%;
	max-width: 45%;
}

.error-page-content{
	text-align: center;
}

.error-page-content .section-title{
	margin-bottom: 15px;
}

/************************************/
/***      31. Responsive css      ***/
/************************************/

@media only screen and (max-width: 1600px){
    .bg-section{
        max-width: calc(100% - 40px);
        margin: 0 20px;
    }

    .main-footer{
        margin-bottom: 20px;
    }
}

@media only screen and (max-width: 991px){

	.navbar{
		padding: 20px 0;
	}

	.slicknav_nav li,
	.slicknav_nav ul{
        display: block;
    }

	.responsive-menu,
    .navbar-toggle{
        display: block;
    }

    .navbar-brand{
        margin: 0;
    }

	.header-btn{
		display: none;
	}

	.btn-default{
		padding: 14px 35px 14px 15px;
	}

    .btn-default::before{
        right: 15px;
    }

    .bg-section{
        max-width: 100%;
        margin: 0;
        border-radius: 0;
    }

	.section-row{
		margin-bottom: 40px;
	}

	.section-title{
		margin-bottom: 30px;
	}

	.section-title h3{
        font-size: 12px;
        padding: 6px 16px;
		margin-bottom: 15px;
	}

	.section-title h1{
		font-size: 50px;
	}

	.section-title h2{
		font-size: 38px;
	}

	.section-title p{
		margin-top: 15px;
	}

	.section-title-content{
		margin-top: 15px;
	}

	.section-btn{
		text-align: left;
		margin-top: 15px;
	}

	.section-content-btn .section-btn{
		margin-top: 15px;
	}

	.hero{
		padding: 180px 0 0;
	}

    .hero.hero-bg-image{
        padding: 170px 0 80px;
    }

    .hero-content{
        margin-bottom: 60px;
    }

    .hero-content .section-title.section-title-center{
        margin-bottom: 40px;
    }

    .hero-icon{
        height: 50px;
        width: 50px;
    }

    .hero-btn .btn-default{
        height: 80px;
        width: 80px;
    }

    .hero-btn .btn-default img{
        max-width: 40px;
    }

    .hero-icon-box-1{
        top: -14%;
    }

    .hero-icon img{
        max-width: 24px;
    }

    .hero-images{
        max-width: 100%;
        margin-bottom: -55px;
    }

    .hero-image-1,
    .hero-image-3{
        margin-top: 60px;
    }

    .hero-image-2{
        max-width: 310px;
    }

    .our-scrolling-ticker{
        padding: 15px 0;
    }

    .scrolling-ticker-box{
        --gap: 40px;
    }
    
    .about-us{
        padding: 80px 0;
    }

    .about-image-box{
        margin: 0 0 30px;
    }

    .about-us-counters{
        margin-bottom: 30px;
    }

    .about-counter-item h2{
        font-size: 38px;
    }

    .about-us-image figure img{
        aspect-ratio: 1 / 0.6;
    }

    .about-info-box{
        padding: 15px;
    }

    .about-us-body{
        max-width: 100%;
        margin-top: 40px;
    }

    .about-us-body .icon-box{
        margin-right: 30px;
    }

    .about-us-body .icon-box a img{
        max-width: 70px;
    }

    .about-body-content{
        width: calc(100% - 100px);
    }

    .our-services{
        padding: 50px 0;
    }

    .service-item{
        padding: 30px;
    }

    .service-item .icon-box{
        margin-bottom: 80px;
    }

    .service-item .icon-box img{
        max-width: 50px;
    }

    .service-item-content h3{
        margin-bottom: 15px;
    }
    
    .service-contact-box{
        padding: 30px 15px 0;
    }

    .service-contact-content{
        margin-bottom: 30px;
    }

    .service-contact-content p{
        margin-bottom: 10px;
    }

    .service-image img{
        max-width: 55%;
    }

    .section-footer-text{
        margin-top: 10px;
    }
    
    .why-choose-us{
        padding: 80px 0 0;
    }

    .why-choose-content{
        margin-bottom: 30px;
    }

    .why-choose-list ul{
        gap: 15px 30px;
    }

    .why-choose-list ul li{
        padding-left: 25px;
    }

    .why-choose-list ul li::before{
        font-size: 16px;
    }

    .why-choose-body{
        gap: 30px 100px;
        margin-top: 30px;
    }

    .why-choose-item-list{
        width: calc(100% - 220px);
    }

    .why-choose-item{
        margin-bottom: 20px;
    }

    .why-choose-item .icon-box{
        width: 50px;
        height: 50px;
        margin-right: 15px;
    }

    .why-choose-item .icon-box img{
        max-width: 24px;
    }

    .why-choose-item-content{
        width: calc(100% - 65px);
    }

    .get-started-circle a figure img{
        max-width: 120px;
    }

    .get-started-circle .circle-arrow img{
        max-width: 45px;
    }

    .why-choose-image{
        max-width: 60%;
        margin: 0 auto;
    }

    .why-choose-image::before{
        top: -15%;
    }

    .why-choose-image img{
        margin-bottom: 0;
    }

    .how-it-work{
        padding: 50px 0;
    }

    .how-work-content{
        margin: 0 0 30px;
    }

    .how-work-item{
        margin-bottom: 30px;
    }

    .how-work-item .icon-box{
        width: 50px;
        height: 50px;
        margin-right: 15px;
    }

    .how-work-item .icon-box img{
        max-width: 24px;
    }

    .how-work-item-content{
        width: calc(100% - 65px);
    }

    .how-work-image,
    .how-work-image figure{
        height: auto;
    }

    .how-work-image figure img{
        aspect-ratio: 1 / 0.65;
    }

    .our-projects{
        padding: 80px 0;
    }

    .project-list-box-1{
        margin: 0;
    }

    .project-list-box-2{
        margin-top: 40px;
        margin-left: 0;
    }

    .project-item{
        gap: 30px;
        padding: 20px;
        margin-bottom: 40px;
    }

    .project-content p{
        font-size: 12px;
        padding: 6px 16px;
        margin-bottom: 15px;
    }

    .project-content h3{
        font-size: 18px;
    }

    .our-testimonials{
        padding: 50px 0;
    }

    .our-testimonial-content{
        margin: 0 0 30px;
    }

    .satisfy-client-box{
        margin-top: 30px;
    }

    .satisfy-client-image img{
        max-width: 60px;
    }

    .testimonial-slider{
        margin-left: 0;
    }

    .testimonial-item{
        padding: 30px;
    }

    .testimonial-content{
        padding-bottom: 50px;
        margin-bottom: 20px;
    }

    .testimonial-content h3{
        margin-bottom: 15px;
    }

    .testimonial-btn{
        margin-top: 30px;
    }

    .testimonial-slider .testimonial-button-next,
    .testimonial-slider .testimonial-button-prev{
        width: 50px;
        height: 50px;
    }

    .testimonial-slider .testimonial-button-next::before,
    .testimonial-slider .testimonial-button-prev::before{
        width: 14px;
        height: 14px;
    }

    .our-faqs{
        padding: 80px 0;
    }

    .our-faqs .faq-accordion{
		margin-bottom: 30px;
	}

	.our-faqs .col-lg-6:last-child .faq-accordion{
		margin-bottom: 0;
	}

    .faq-accordion .accordion-item{
        margin-bottom: 20px;
    }

    .faq-accordion .accordion-header .accordion-button{
        padding: 14px 40px 14px 15px;
    }

    .faq-accordion .accordion-item .accordion-button::after,
    .faq-accordion .accordion-item .accordion-button.collapsed::after{
        right: 15px;
        font-size: 16px;
    }
    
    .faq-accordion .accordion-item .accordion-body{
        padding: 6px 40px 15px 15px;
    }

    .cta-box{
        padding: 50px 0;
    }

    .cta-box-content{
        margin-bottom: 30px;
    }

    .cta-box-btn::before{
        bottom: -5px;
        width: 70px;
        height: 70px;
    }

    .cta-box-image{
        width: 100%;
        max-width: 60%;
        margin: 0 auto;
    }

    .cta-box-image::before{
        top: -20px;
        background-size: contain;
    }

    .cta-box-image img{
        margin-bottom: -50px;
    }

    .our-blog{
        padding: 80px 0 50px;
    }

    .post-item{
        padding: 20px;
    }

    .main-footer{
        padding: 50px 0 0;
    }

    .footer-header{
        padding: 25px;
        margin-bottom: 40px;
    }

    .footer-header-title h2{
        font-size: 50px;
    }

    .footer-header .hero-btn .btn-default{
        height: 70px;
        width: 70px;
    }
 
    .footer-header .hero-btn .btn-default img{
        max-width: 40px;
    }

    .footer-contact-box{
        margin-right: 0;
    }

    .footer-links{
        margin-bottom: 30px;
    }

    .footer-links h3{
        margin-bottom: 15px;
    }

    .footer-links ul li b{
       font-size: 22px;
    }

    .footer-copyright{
        padding: 30px 0;
        margin-top: 0;
    }

    .page-header{
        padding: 150px 0 60px;
    }
    
    .page-header-box h1{
        font-size: 50px;
    }

    .our-scrolling-ticker.subpages-scrolling-ticker{
        padding: 10px 0;
    }

    .our-approach{
        padding: 50px 0;
    }

    .our-approach-content{
        position: initial;
        margin: 0 0 30px;
    }

    .approach-item{
        padding: 30px;
    }

    .approach-item-content,
    .approach-item .icon-box{
        margin-bottom: 20px;
    }

    .approach-item-content h3{
        margin-bottom: 15px;
    }

    .approach-item .icon-box img{
        max-width: 50px;
    }

    .our-values{
        padding: 80px 0;
    }

    .value-box-list{
        margin-bottom: 30px;
    }

    .value-box-item{
        padding: 30px;
    }

    .value-box-header{
        margin-bottom: 40px;
    }

    .value-box-header h2{
        font-size: 38px;
    }

    .value-box-body ul li{
        padding-left: 25px;
        margin-bottom: 10px;
    }

    .value-box-body ul li::before{
        font-size: 16px;
    }

    .value-image{
        margin-left: 0;
    }

    .value-image img{
        aspect-ratio: 1 / 0.65;
    }

    .what-drives-us{
        padding: 50px 0;
    }

    .what-drives-image{
        margin-bottom: 30px;
    }

    .what-drives-image,
    .what-drives-image figure,
    .what-drives-image figure img{
        height: auto;
    }

    .what-drives-content{
        padding: 30px;
    }

    .what-drives-list ul li{
        padding-left: 25px;
    }

    .what-drives-list ul li::before{
        font-size: 16px;
    }

    .who-we-are{
        padding: 80px 0;
    }

    .brand-logo-item{
        padding: 20px 15px;
    }

    .brand-logo-item{
        width: 20%;
    }

    .our-team{
        padding: 80px 0 50px;
    }

    .team-image{
        padding: 20px 20px 0;
        margin-bottom: 15px;
    }

    .team-content{
        margin-bottom: 10px;
    }

    .page-services{
        padding: 80px 0 50px;
    }

    .page-service-single{
        padding: 80px 0;
    }

    .page-single-sidebar{
        position: initial;
        margin: 0 0 30px;
    }

    .page-catagery-list{
        margin-bottom: 30px;
    }

    .page-catagery-list h3,
    .page-catagery-list ul{
        padding: 20px;
    }

    .page-catagery-list ul li{
        margin-bottom: 15px;
        padding-bottom: 15px;
    }

    .page-catagery-list ul li a{
        padding-right: 25px;
    }

    .page-catagery-list ul li a::before{
        width: 12px;
        height: 12px;
    }

    .service-contact-box.sidebar-cta-box{
        padding-top: 30px;
    }

    .page-single-image{
        margin-bottom: 30px;
    }

    .service-entry{
        margin-bottom: 40px;
    }

    .service-entry p{
        margin-bottom: 15px;
    }

    .service-entry h2{
        font-size: 38px;
        margin-bottom: 15px;
    }

    .service-entry ul{
        gap: 10px 20px;
    }

    .service-entry ul li{
        width: calc(50% - 10px);
        font-size: 14px;
        padding-left: 25px;
    }

    .service-entry ul li::before{
        font-size: 16px;
    }

    .data-driven-box,
    .service-decision-box,
    .service-insight-box{
        margin-top: 40px;
    }

    .data-driven-box ul{
        margin: 30px 0;
    }

    .data-driven-item-list{
        padding: 20px;
    }

    .data-driven-item{
        margin-bottom: 30px;
        padding-bottom: 30px;
    }

    .data-driven-item .icon-box{
        width: 50px;
        height: 50px;
        margin-right: 15px;
    }

    .data-driven-item .icon-box img{
        max-width: 26px;
    }

    .data-driven-item-content{
        width: calc(100% - 65px);
    }

    .data-driven-item-content h3{
        margin-bottom: 10px;
    }

    .service-decision-image-content{
        margin: 30px 0;
    }

    .service-decision-item{
        padding: 20px;
    }

    .service-decision-item .icon-box{
        margin-bottom: 30px;
    }

    .service-decision-item .icon-box img{
        max-width: 50px;
    }

    .service-decision-item-content h3{
        font-size: 18px;
    }

    .service-insight-images{
        margin: 30px 0;
    }

    .page-blog{
        padding: 80px 0;
    }
    
    .page-pagination{
        margin-top: 10px;
    }

    .page-single-post{
        padding: 80px 0;
    }
    
    .post-image{
        margin-bottom: 20px;
    }

    .post-image figure,
    .post-image img{
        border-radius: 20px;
    }
    
    .post-entry h1,
    .post-entry h2,
    .post-entry h3,
    .post-entry h4,
    .post-entry h5,
    .post-entry h6{
        margin: 0 0 0.45em;
    }
    
    .post-entry h2{
        font-size: 38px;
    }
    
    .post-entry p{
        margin-bottom: 15px;
    }
    
    .post-entry ol li,
    .post-entry ul li{
        margin-bottom: 10px;
    }
    
    .post-entry blockquote{
        background-position: 20px 20px;
        background-size: 40px;
        padding: 20px 20px 20px 70px;
        margin-bottom: 20px;
    }
    
    .post-entry blockquote p{
        font-size: 18px;
    }
    
    .post-tags{
        margin-bottom: 20px;
    }
    
    .post-tags .tag-links a{
        padding: 12px 15px;
    }
    
    .post-social-sharing ul{
        text-align: left;
    }

    .page-projects{
        padding: 80px 0;
    }

    .page-project-list{
        gap: 40px;
    }

    .page-project-list .project-item{
        width: calc(50% - 20px);
    }

    .page-project-single{
        padding: 80px 0;
    }

    .project-entry{
        margin-bottom: 40px;
    }

    .project-entry p{
        margin-bottom: 15px;
    }

    .project-entry h2{
        font-size: 38px;
        margin-bottom: 15px;
    }

    .project-entry ul{
        gap: 10px 20px;
    }

    .project-entry ul li{
        width: calc(50% - 10px);
        padding-left: 25px;
    }

    .project-entry ul li::before{
        font-size: 16px;
    }

    .project-integration-box,
    .project-growth-box,
    .project-demand-box{
        margin-top: 40px;
    }

    .project-integration-item-list{
        margin: 30px 0;
    }

    .project-integration-item .why-choose-item{
        margin-bottom: 20px;
    }

    .project-growth-content-box{
        padding: 30px;
        margin-top: 30px;
    }

    .project-growth-item-list{
        gap: 30px;
    }

    .project-growth-item-list .how-work-item{
        width: calc(33.33% - 20px);
    }

    .project-growth-item-list .how-work-item .icon-box{
        margin: 0 0 30px;
    }

    .project-growth-list{
        padding-top: 30px;
        margin-top: 30px;
    }

    .project-demand-item-box{
        margin-top: 30px;
        padding: 30px;
    }

    .project-demand-list-item{
        margin-bottom: 30px;
        padding-bottom: 30px;
    }

    .project-demand-content ul li{
        width: 100%;
        font-size: 14px;
    }

    .page-team{
        padding: 80px 0 50px;
    }

    .page-team-single{
        padding: 80px 0;
    }

    .team-single-image img{
        max-width: 280px;
    }

    .team-member-about,
    .team-member-experience,
    .team-member-skills{
        margin-bottom: 40px;
    }

    .member-contact-box{
        padding: 20px;
    }

    .member-contact-box .icon-box{
        width: 50px;
        height: 50px;
        margin-right: 15px;
    }

    .member-contact-box .icon-box img{
        max-width: 24px;
    }

    .member-contact-content{
        width: calc(100% - 65px);
    }

    .member-contact-content h3{
        margin-bottom: 5px;
    }

    .member-social-list{
        margin-top: 30px;
        gap: 20px;
    }

    .team-experience-list ul li{
        padding-left: 25px;
        margin-bottom: 10px;
    }

    .team-experience-list ul li::before{
        font-size: 16px;
    }

    .member-skill-list{
        gap: 30px;
    }

    .page-testimonials{
        padding: 80px 0 50px;
    }

    .page-gallery{
		padding: 80px 0 50px;
	}

	.page-video-gallery{
		padding: 80px 0 50px;
	}

    .page-faqs{
        padding: 80px 0;
    }
    
    .page-faqs .page-faq-accordion{
        margin-bottom: 40px;
    }

    .page-contact-us{
        padding: 80px 0 40px;
    }

    .contact-us-content{
        margin: 0 0 30px;
    }

    .contact-info-body{
        gap: 30px;
    }

    .contact-info-item .icon-box{
        width: 50px;
        height: 50px;
        margin-right: 15px;
    }

    .contact-info-item .icon-box img{
        max-width: 24px;
    }

    .contact-item-content{
        width: calc(100% - 65px);
    }

    .contact-info-item.location-item{
        padding-top: 30px;
    }

    .contact-us-form{
        padding: 30px;
    }

    .contact-form form .form-control{
        padding: 12px 15px;
    }

    .contact-form form .btn-default{
        padding: 14px;
    }

    .google-map-iframe{
        margin-top: 80px;
    }

    .google-map-iframe,
    .google-map-iframe iframe{
        height: 450px;
    }

    .error-page{
		padding: 80px 0;
	}
	
	.error-page-image{
		margin-bottom: 20px;
	}

	.error-page-image img{
		max-width: 80%;
	}
}

@media only screen and (max-width: 767px){

	.section-row{
		margin-bottom: 30px;
	}

    .section-title.section-title-center{
		max-width: 100%;
	}

	.section-title h1{
		font-size: 30px;
	}

	.section-title h2{
		font-size: 26px;
	}

	.section-title p{
        margin-top: 10px;
    }

	.section-title-content{
        margin-top: 10px;
    }

    .hero-content .section-title.section-title-center{
        margin-bottom: 30px;
    }

    .hero-content{
        margin-bottom: 40px;
    }

    .hero-btn .btn-default{
        height: 60px;
        width: 60px;
    }

    .hero-btn .btn-default img{
        max-width: 32px;
    }
    
    .hero-icon-box-1{
        max-width: 100%;
    }

    .hero-icon{
        height: 45px;
        width: 45px;
    }

    .hero-icon img{
        max-width: 20px;
    }
    
    .hero-icon-1,
    .hero-icon-2{
        animation-duration: 4s;
    }
    
    @keyframes iconheroimgdown{
        50%{
            transform: translateY(30px)
        }
    }

    .hero-icon-box-2{
        bottom: 0;
    }
    
    .hero-images{
        margin-bottom: 0px;
    }

    .hero-image-1,
    .hero-image-3{
        margin-top: 30px;
    }

    .hero-image-2{
        max-width: 170px;
    }
    
    .our-scrolling-ticker{
        padding: 10px 0;
    }

    .scrolling-ticker-box{
        --gap: 30px;
    }

    .scrolling-content img{
        height: 35px;
    }
    
    .about-us-counters{
        gap: 30px;
        margin-bottom: 20px;
    }

    .about-counter-item h2{
        font-size: 26px;
        margin-bottom: 5px;
    }

    .about-us-image figure img{
        aspect-ratio: 1 / 0.75;
    }

    .about-info-box{
        padding: 10px;
        bottom: 15px;
        left: 15px;
    }

    .about-us-body{
        margin-top: 30px;
    }

    .about-us-body .icon-box{
        margin-right: 10px;
    }

    .about-us-body .icon-box a img{
        max-width: 50px;
    }

    .about-body-content{
        width: calc(100% - 60px);
    }

    .about-body-content p{
        font-size: 14px;
    }

    .service-item{
        padding: 20px;
    }

    .service-item .icon-box{
        margin-bottom: 40px;
    }
    
    .service-item-content h3{
        font-size: 18px;
        margin-bottom: 10px;
    }

    .service-contact-box{
        padding: 20px 15px 0;
    }

    .service-contact-content{
        margin-bottom: 20px;
        margin-left: 5px;
    }

    .service-contact-content h3{
        font-size: 18px;
    }

    .service-image img{
        max-width: 90%;
    }

    .section-footer-text span{
        font-size: 14px;
        margin-right: 5px;
    }
 
    .why-choose-list ul{
        gap: 10px;
    }

    .why-choose-list ul li{
        width: 100%;
    }

    .why-choose-body{
        gap: 20px;
    }

    .why-choose-item-list{
        width: calc(100% - 120px);
    }

    .why-choose-item{
        margin-bottom: 20px;
    }

    .why-choose-item-content h3{
        font-size: 18px;
    }

    .get-started-circle a figure img{
        max-width: 100px;
    }

    .get-started-circle .circle-arrow img{
        max-width: 40px;
    }

    .why-choose-image{
        padding: 0 10px;
        max-width: 75%;
    }

    .how-work-item-content h3{
        font-size: 18px;
        margin-bottom: 5px;
    }

    .how-work-item-content p{
        font-size: 14px;
    }

    .project-item{
        padding: 20px;
        margin-bottom: 30px;
    }

    .project-content h3{
        font-size: 18px;
    }

    .project-list-box-2{
        margin-top: 30px;
    }

    .satisfy-client-image img{
        max-width: 50px;
    }

    .testimonial-item{
        padding: 20px;
    }

    .testimonial-content{
        padding-bottom: 30px;
    }

    .author-content h3,
    .testimonial-content h3{
        font-size: 18px;
    }

    .testimonial-btn{
        margin-top: 20px;
    }

    .testimonial-slider .testimonial-button-next,
    .testimonial-slider .testimonial-button-prev{
        width: 40px;
        height: 40px;
    }

    .testimonial-slider .testimonial-button-next{
        margin-left: 10px;
    }

    .testimonial-slider .testimonial-button-next::before,
    .testimonial-slider .testimonial-button-prev::before{
        width: 12px;
        height: 12px;
    }

    .faq-accordion .accordion-header .accordion-button{
        font-size: 16px;
    }

    .faq-accordion .accordion-item .accordion-body{
        padding: 6px 15px 15px 15px;
    }

    .faq-accordion .accordion-item .accordion-body p{
        font-size: 14px;
    }

    .cta-box-image{
        max-width: 100%;
    }

    .post-featured-image{
        margin-bottom: 15px;
    }

    .post-item-content h2{
        font-size: 18px;
    }

    .footer-header{
        padding: 15px;
    }

    .footer-header-title h2{
        font-size: 30px;
    }

    .footer-header .hero-btn .btn-default{
        height: 60px;
        width: 60px;
    }

    .footer-header .hero-btn .btn-default img{
        max-width: 35px;
    }

    .footer-contact-box{
        display: block;
    }

    .footer-contact-box .footer-links{
        width: 100%;
    }

    .footer-links h3{
        font-size: 18px;
    }

    .footer-links ul li b{
        font-size: 20px;
    }

    .footer-copyright{
        text-align: center;
        padding: 15px 0;
    }

    .footer-copyright-text{
        text-align: center;
        margin-top: 10px;
    }

    .page-header-box h1{
        font-size: 30px;
    }

    .page-header-box ol li.breadcrumb-item{
        font-size: 16px;
    }

    .approach-item{
        width: 100%;
        padding: 20px;
    }

    .approach-item-content h3{
        font-size: 18px;
    }

    .value-box-item{
        width: 100%;
        padding: 20px;
    }

    .value-box-header{
        margin-bottom: 30px;
    }

    .value-box-header h2{
        font-size: 26px;
    }

    .value-image img{
        aspect-ratio: 1 / 0.8;
    }
    
    .what-drives-image img{
        aspect-ratio: 1 / 0.75;
    }

    .what-drives-content{
        padding: 20px;
    }

    .brand-logo-item{
        width: 50%;
    }

    .brand-logo-item img{
        height: 30px;
    }

    .team-content h3{
        font-size: 18px;
    }

    .page-catagery-list h3{
        font-size: 18px;
    }

    .page-single-image{
        margin-bottom: 20px;
    }

    .page-single-image img{
        aspect-ratio: 1 / 0.7;
    }

    .service-entry h2{
        font-size: 26px;
    }

    .service-entry ul li{
        width: 100%;
    }

    .data-driven-item-list{
        padding: 15px;
    }

    .data-driven-item{
        margin-bottom: 20px;
        padding-bottom: 20px;
    }

    .data-driven-item-content h3{
        font-size: 18px;
    }

    .service-decision-item,
    .service-decision-image{
        width: 100%;
    }

    .service-decision-item .icon-box{
        margin-bottom: 20px;
    }

    .service-decision-image figure,
    .service-decision-image img{
        height: auto;
    }

    .service-insight-images{
        gap: 20px;
    }

    .service-insight-img{
        width: calc(50% - 10px);
    }

    .post-single-meta ol li{
        font-size: 16px;
    }
    
    .post-single-meta ol li i{
        font-size: 16px;
    }
    
    .post-image img{
        aspect-ratio: 1 / 0.7;
    }
    
    .post-entry blockquote{
        background-position: 15px 15px;
        padding: 60px 15px 15px 15px;
    }
    
    .post-entry blockquote p{
        font-size: 16px;
    }
    
    .post-entry h2{
        font-size: 26px;
    }

	.tag-links{
        font-size: 18px;
    }

    .page-project-list{
        gap: 30px;
    }

    .page-project-list .project-item{
        width: 100%;
    }

    .project-entry h2{
        font-size: 26px;
    }

    .project-entry ul li{
        width: 100%;
    }

    .project-integration-item{
        width: 100%;
    }

    .project-integration-item .why-choose-item{
        margin-bottom: 15px;
    }

    .project-growth-content-box{
        padding: 20px;
    }

    .project-growth-item-list{
        gap: 20px;
    }

    .project-growth-item-list .how-work-item{
        width: 100%;
        display: flex;
    }  
    
    .project-growth-item-list .how-work-item .icon-box{
        margin: 0 15px 0 0;
    }

    .project-growth-item-list .how-work-item-content{
        width: calc(100% - 65px);
    }

    .project-growth-list{
        padding-top: 20px;
    }

    .project-demand-item-box{
        padding: 20px;
    }

    .project-demand-list-item{
        gap: 20px;
    }

    .project-demand-image,
    .project-demand-content{
        width: 100%;
    }

    .project-demand-image img{
        aspect-ratio: 1 / 0.65;
    }

    .member-contact-box{
        width: 100%;
        padding: 15px;
    }

    .member-contact-content h3,
    .member-social-list h3{
        font-size: 18px;
    }

    .member-social-list{
        gap: 15px;
    }

    .member-social-list ul li{
        margin-right: 5px;
    }

    .member-skill-list{
        gap: 20px;
    }

    .skills-progress-bar{
        width: 100%;
    }

    .skills-progress-bar .skill-data{
        margin-bottom: 10px;
    }

    .contact-info-body{
        gap: 20px;
    }

    .contact-info-item{
        width: 100%;
    }

    .contact-info-item.location-item{
        padding-top: 0;
        border: none;
    }

    .contact-us-form{
        padding: 20px;
    }

    .google-map-iframe,
    .google-map-iframe iframe{
        height: 350px;
    }
}

/************************************/
/*** 	Live Market Data css	  ***/
/************************************/

.live-market-data{
    padding: 100px 0;
}

.market-data-container{
    background: var(--secondary-color);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid var(--divider-color);
}

.market-data-table{
    margin-bottom: 40px;
}

.table-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0 15px;
}

.table-header h4{
    color: var(--primary-color);
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.market-data-table .table{
    background: transparent;
    border: none;
    margin-bottom: 0;
}

.market-data-table .table thead th{
    background: var(--bg-color);
    color: var(--primary-color);
    border: none;
    padding: 20px 15px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 2px solid var(--divider-color);
}

.market-data-table .table tbody td{
    background: transparent;
    color: var(--text-color);
    border: none;
    padding: 20px 15px;
    vertical-align: middle;
    border-bottom: 1px solid var(--divider-color);
    font-size: 14px;
}

.market-data-table .table tbody tr:hover{
    background: var(--divider-color);
}

.market-data-table .table tbody tr{
    transition: all 0.3s ease;
    position: relative;
}

.market-data-table .table tbody tr.price-updated{
    background: linear-gradient(90deg, transparent 0%, rgba(226, 76, 74, 0.2) 50%, transparent 100%);
    animation: priceFlash 1s ease-out;
}

.market-data-table .table tbody tr.price-updated-positive{
    background: linear-gradient(90deg, transparent 0%, rgba(0, 212, 170, 0.2) 50%, transparent 100%);
    animation: priceFlashPositive 1s ease-out;
}

@keyframes priceFlash {
    0% { background: linear-gradient(90deg, transparent 0%, rgba(226, 76, 74, 0.4) 50%, transparent 100%); }
    100% { background: transparent; }
}

@keyframes priceFlashPositive {
    0% { background: linear-gradient(90deg, transparent 0%, rgba(0, 212, 170, 0.4) 50%, transparent 100%); }
    100% { background: transparent; }
}

.live-indicator{
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-left: 10px;
}

.live-dot{
    width: 8px;
    height: 8px;
    background: #00D4AA;
    border-radius: 50%;
    animation: livePulse 2s infinite;
}

@keyframes livePulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.live-text{
    color: #00D4AA;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.price-cell{
    position: relative;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.price-blink{
    animation: priceBlink 0.5s ease-in-out;
}

@keyframes priceBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.change-cell{
    position: relative;
}

.change-arrow{
    display: inline-block;
    margin-right: 4px;
    animation: bounceArrow 1s ease-in-out infinite;
}

@keyframes bounceArrow {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
}

.volume-bar{
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--accent-secondary-color) 100%);
    animation: volumeFlow 3s linear infinite;
}

@keyframes volumeFlow {
    0% { width: 0%; opacity: 0.8; }
    50% { width: 100%; opacity: 1; }
    100% { width: 0%; opacity: 0.8; }
}

.market-ticker{
    background: var(--bg-color);
    border: 1px solid var(--divider-color);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    overflow: hidden;
    position: relative;
}

.ticker-content{
    display: flex;
    animation: tickerScroll 30s linear infinite;
    white-space: nowrap;
}

@keyframes tickerScroll {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.ticker-item{
    display: inline-flex;
    align-items: center;
    margin-right: 40px;
    color: var(--text-color);
    font-size: 14px;
}

.ticker-symbol{
    color: var(--primary-color);
    font-weight: 600;
    margin-right: 8px;
}

.ticker-price{
    margin-right: 8px;
    font-family: 'Courier New', monospace;
}

.data-stream{
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(226, 76, 74, 0.1) 100%);
    animation: dataFlow 2s linear infinite;
}

@keyframes dataFlow {
    0% { transform: translateX(-100px); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100px); opacity: 0; }
}

.coin-info{
    display: flex;
    align-items: center;
    gap: 12px;
}

.coin-logo{
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.coin-details h6{
    color: var(--primary-color);
    margin: 0;
    font-weight: 600;
    font-size: 14px;
}

.coin-details span{
    color: var(--text-color);
    font-size: 12px;
    text-transform: uppercase;
}

.price-positive{
    color: #00D4AA !important;
}

.price-negative{
    color: var(--accent-color) !important;
}

.change-positive{
    background: #00D4AA20;
    color: #00D4AA;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.change-negative{
    background: #E24C4A20;
    color: var(--accent-color);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.mini-chart{
    width: 80px;
    height: 40px;
}

.loading-spinner{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 40px 0;
}

.spinner{
    width: 40px;
    height: 40px;
    border: 3px solid var(--divider-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin{
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.market-chart-container{
    margin-top: 40px;
}

.chart-box{
    background: var(--bg-color);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid var(--divider-color);
    height: 100%;
}

.chart-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.chart-header h4{
    color: var(--primary-color);
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.chart-controls{
    display: flex;
    gap: 10px;
}

.chart-btn{
    background: transparent;
    border: 1px solid var(--divider-color);
    color: var(--text-color);
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-btn:hover,
.chart-btn.active{
    background: linear-gradient(135deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
    border-color: var(--accent-color);
    color: var(--primary-color);
}

.chart-content{
    position: relative;
    height: 300px;
}

.market-stats{
    background: var(--bg-color);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid var(--divider-color);
    height: 100%;
}

.market-stats h4{
    color: var(--primary-color);
    margin-bottom: 25px;
    font-size: 18px;
    font-weight: 600;
}

.stat-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--divider-color);
}

.stat-item:last-child{
    border-bottom: none;
}

.stat-label{
    color: var(--text-color);
    font-size: 14px;
}

.stat-value{
    color: var(--primary-color);
    font-weight: 600;
    font-size: 14px;
}

.trading-signal{
    margin-top: 30px;
    padding-top: 25px;
    border-top: 2px solid var(--divider-color);
}

.trading-signal h5{
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.signal-indicator{
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--accent-secondary-color)20 0%, var(--accent-color)20 100%);
}

.signal-status{
    color: var(--primary-color);
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.signal-buy{
    background: linear-gradient(135deg, #00D4AA20 0%, #00D4AA40 100%);
}

.signal-sell{
    background: linear-gradient(135deg, #E24C4A20 0%, #E24C4A40 100%);
}

.signal-hold{
    background: linear-gradient(135deg, #FFA50020 0%, #FFA50040 100%);
}

/* Responsive styles for market data */
@media (max-width: 991px) {
    .live-market-data{
        padding: 60px 0;
    }

    .market-data-container{
        padding: 30px 20px;
    }

    .chart-header{
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .chart-controls{
        width: 100%;
        justify-content: center;
    }

    .market-chart-container .row{
        flex-direction: column-reverse;
    }

    .market-stats{
        margin-bottom: 30px;
    }
}

@media (max-width: 767px) {
    .market-data-table{
        overflow-x: auto;
    }

    .market-data-table .table{
        min-width: 700px;
    }

    .market-data-table .table thead th,
    .market-data-table .table tbody td{
        padding: 15px 10px;
        font-size: 12px;
    }

    .coin-info{
        gap: 8px;
    }

    .coin-logo{
        width: 24px;
        height: 24px;
    }

    .coin-details h6{
        font-size: 12px;
    }

    .coin-details span{
        font-size: 10px;
    }

    .chart-box,
    .market-stats{
        padding: 20px;
    }

    .chart-content{
        height: 250px;
    }
}

/************************************/
/*** 	Mobile Download Button	  ***/
/************************************/

.mobile-download-btn{
    display: none;
}

/* Show mobile download button only on mobile devices */
@media (max-width: 991px) {
    .mobile-download-btn{
        display: block;
        margin-left: auto;
        margin-right: 15px;
    }

    .mobile-download-btn .btn-default{
        padding: 8px 16px;
        font-size: 14px;
        min-width: auto;
        white-space: nowrap;
    }

    .mobile-download-btn .btn-default i{
        margin-right: 5px;
        font-size: 12px;
    }

    /* Adjust header layout for mobile */
    .navbar{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .navbar .container{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .navbar-brand{
        flex-shrink: 0;
    }

    .navbar-toggle{
        flex-shrink: 0;
        margin-left: 15px;
    }
}